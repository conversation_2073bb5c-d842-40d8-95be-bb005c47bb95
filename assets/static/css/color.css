::-webkit-scrollbar-thumb {
    background: rgba(199, 199, 199, 0.8);
}

.layui-bg-blue {
    background-color: #58b7ff !important;
}

.layui-btn {
    background-color: #409eff;
}

.layui-layer-btn .layui-layer-btn0 {
    background-color: #409eff;
}

.layui-btn-primary {
    background-color: transparent;
}

.layui-btn-primary:hover {
    border-color: #79bbff;
}

.layui-input:focus,
.layui-textarea:focus {
    border-color: #79bbff !important;
    box-shadow: none;
}

.layui-form-danger + .layui-form-select .layui-input,
.layui-form-danger:focus {
    border-color: #ff5722 !important;
}

.layui-laypage a:hover {
    color: #409eff;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: #409eff;
}

.layui-laypage input:focus, .layui-laypage select:focus {
    border-color: #79bbff !important;
    box-shadow: none;
}

.layui-table-view .layui-table td[data-edit]:hover:after {
    border-color: #79bbff;
}

.layui-form-checkbox[lay-skin=primary]:hover > i {
    border-color: #79bbff;
}

.layui-form-checkbox[lay-skin=primary] > .layui-icon-indeterminate:before {
    background-color: #79bbff;
}

.layui-form-checkbox[lay-skin=primary] > .layui-icon-indeterminate {
    border-color: #79bbff;
}

.layui-form-checked[lay-skin=primary] > i {
    background-color: #409eff;
    border-color: #409eff !important;
}

.layui-table-checked {
    background-color: #ecf5ff;
}

.layui-table-checked:hover {
    background-color: #d9ecff;
}

.layui-table-cell-c:hover {
    border-color: #79bbff;
}

.layui-table-checked.layui-table-click,
.layui-table-checked.layui-table-hover {
    background-color: #d9ecff;
}

.layui-nav-tree .layui-nav-child dd.layui-this,
.layui-nav-tree .layui-nav-child dd.layui-this a,
.layui-nav-tree .layui-this,
.layui-nav-tree .layui-this > a,
.layui-nav-tree .layui-this > a:hover {
    background-color: #409eff;
}

.layui-nav-tree .layui-nav-bar {
    background-color: #409eff;
}

.proxy-expand .layui-row .layui-row > div:first-child {
    color: #99a9bf;
}

section.server-info .text-row .text-col:first-child {
    color: #99a9bf;
}

section.proxy-list .proxy-info .layui-row .layui-row > div:first-child {
    color: #99a9bf;
}

.online,
.offline {
    border-radius: 4px;
    padding: 2px 10px;
    font-size: 12px;
    border-width: 1px;
    border-style: solid;
}

.online {
    color: #67c23a;
    background-color: #f0f9eb;
    border-color: #e1f3d8;
}

.offline {
    color: #f56c6c;
    background-color: #fef0f0;
    border-color: #fde2e2;
}

.version a,
.version span {
    color: #99a9bf;
}

.login-title,
.login-title a {
    color: #333 !important;
}

@media (prefers-color-scheme: dark) {
    ::-webkit-scrollbar-thumb {
        background: rgba(107, 107, 107, 0.8);
    }

    .login-title,
    .login-title a {
        color: #99a9bf !important;
    }

    .layui-bg-blue {
        background-color: #395c74 !important;
    }

    .layui-btn {
        background-color: #4f80a1;
    }

    .layui-layer-btn .layui-layer-btn0 {
        background-color: #4f80a1;
    }

    .layui-btn-primary {
        background-color: transparent;
        border-color: #484849;
    }

    .layui-btn-primary:hover {
        border-color: #5f5f60;
    }

    .layui-input:focus,
    .layui-textarea:focus {
        border-color: #5f5f60 !important;
        box-shadow: none;
    }

    .layui-laypage a:hover {
        color: #4f80a1;
    }

    .layui-laypage .layui-laypage-curr .layui-laypage-em {
        background-color: #4f80a1;
    }

    .layui-laypage input:focus, .layui-laypage select:focus {
        border-color: #5f5f60 !important;
        box-shadow: none;
    }

    .layui-table-view .layui-table td[data-edit]:hover:after {
        border-color: #5f5f60;
    }

    .layui-form-danger + .layui-form-select .layui-input,
    .layui-form-danger:focus {
        border-color: #ff5722 !important;
    }

    .layui-form-checkbox[lay-skin=primary]:hover > i {
        border-color: #5f5f60;
    }

    .layui-form-checkbox[lay-skin=primary] > .layui-icon-indeterminate:before {
        background-color: #5f5f60;
    }

    .layui-form-checkbox[lay-skin=primary] > .layui-icon-indeterminate {
        border-color: #5f5f60;
    }

    .layui-form-checked[lay-skin=primary] > i {
        background-color: #484849;
        border-color: #484849 !important;
    }

    .layui-table-checked {
        background-color: rgba(255, 255, 255, .04);
    }

    .layui-table-checked:hover {
        background-color: rgba(255, 255, 255, .08);
    }

    .layui-table-cell-c:hover {
        border-color: #5f5f60;
    }

    .layui-table-checked.layui-table-click,
    .layui-table-checked.layui-table-hover {
        background-color: #5f5f60;
    }

    .layui-nav-tree .layui-nav-child dd.layui-this,
    .layui-nav-tree .layui-nav-child dd.layui-this a,
    .layui-nav-tree .layui-this,
    .layui-nav-tree .layui-this > a,
    .layui-nav-tree .layui-this > a:hover {
        background-color: #4f80a1;
    }

    .layui-nav-tree .layui-nav-bar {
        background-color: #4f80a1;
    }

    .online {
        color: #67c23a;
        background-color: #1c2518;
        border-color: #25371c;
    }

    .offline {
        color: #f56c6c;
        background-color: #2b1d1d;
        border-color: #412626;
    }
}