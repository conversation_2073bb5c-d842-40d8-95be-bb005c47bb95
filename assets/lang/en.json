{"Frps Panel": "Frps Panel", "User": "User", "Token": "Token", "Notes": "Notes", "Search": "Search", "Reset": "Reset", "New user": "New user", "Remove user": "Remove user", "Disable user": "Disable user", "will take sometime to make effective": " will take sometime to make effective", "Enable user": "Enable user", "Remove": "Remove", "Disable": "Disable", "Enable": "Enable", "Please input user account": "Please input user account", "Please input user token": "Please input user token", "Please input user notes": "Please input user notes", "Status": "Status", "Operation": "Operation", "Confirm": "Confirm", "Cancel": "Cancel", "Confirm to remove user": "Confirm to remove user ?", "Confirm to disable user": "Confirm to disable user ?", "Confirm to enable user": "Confirm to enable user ?", "Operate success": "Operate success", "Operate failed": "Operate failed", "Operate error": "Operate error", "Other error": "Other error", "Param error": "Param error", "User exist": "User exist", "User not exist": "User not exist", "User format error": "User cannot be empty or include space char. It only allowed alphanumeric and underline.", "Token format error": "Token cannot be empty or include space char. It allow include those special char: _!@#$%^&*()", "Please check at least one user": "Please Check at least one user", "Operation confirm": "Operation confirm", "Empty data": "Empty data", "Allowed ports": "Allowed ports", "Please input allowed ports": "Please input allowed ports, example: 8081, 9000-9010", "Allowed domains": "Allowed domains", "Please input allowed domains": "Please input allowed domains, example: web01.domain.com,web02.domain.com", "Allowed subdomains": "Allowed subdomains", "Please input allowed subdomains": "Please input allowed subdomains, example: web01,web02", "Ports is invalid": "Ports is invalid", "Domains is invalid": "Domains is invalid", "Subdomains is invalid": "Subdomains is invalid", "Comment is invalid": "Comment is invalid, it cannot include line breaks", "Not limit": "Not limit", "None": "None", "Server Info": "Server Info", "Users": "Users", "Proxies": "Proxies", "Name": "Name", "Port": "Port", "Connections": "Connections", "Traffic In": "Traffic In", "Traffic Out": "Traffic Out", "Client Version": "Client Version", "Traffic Statistics": "Traffic Statistics", "Type": "Type", "Domains": "Domains", "SubDomain": "SubDomain", "Locations": "Locations", "HostRewrite": "HostRewrite", "Encryption": "Encryption", "Compression": "Compression", "Addr": "Addr", "online": "online", "offline": "offline", "true": "Yes", "false": "No", "Last Start": "Last Start", "Last Close": "Last Close", "Network Traffic": "Network Traffic", "today": "today", "now": "now", "Version": "Version", "Bind Port": "Bind Port", "KCP Bind Port": "KCP Bind Port", "QUIC Bind Port": "QUIC Bind Port", "HTTP Port": "HTTP Port", "HTTPS Port": "HTTPS Port", "TCPMUX Port": "TCPMUX Port", "Subdomain Host": "Subdomain Host", "Max Pool Count": "Max Pool Count", "Max Ports Per Client": "Max Ports Per Client", "Heart Beat Timeout": "Heart Beat Timeout", "Allow Ports": "Allow Ports", "TLS Only": "TLS Only", "Current Connections": "Current Connections", "Client Counts": "Client Counts", "Proxy Counts": "Proxy Counts", "Not Set": "Not Set", "Proxy": "Proxies", "Username": "Username", "Password": "Password", "Login": "<PERSON><PERSON>", "Please input username": "Please input username", "Please input password": "Please input password", "Login success": "Login success", "Username or password incorrect": "Username or password incorrect", "Token invalid": "<PERSON><PERSON> invalid", "Total": "Total ", "Items": " items", "Go to": "Go to", "Per Page": " / page"}