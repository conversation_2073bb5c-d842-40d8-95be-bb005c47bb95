{"Frps Panel": "Frps 面板", "User": "用户名(user)", "Token": "凭证(meta_token)", "Notes": "备注", "Search": "搜索", "Reset": "重置", "New user": "新增用户", "Remove user": "删除用户", "Disable user": "禁用用户", "will take sometime to make effective": "需要一定时间才会生效", "Enable user": "启用用户", "Remove": "删除", "Disable": "禁用", "Enable": "启用", "Please input user account": "请输入用户名(user)", "Please input user token": "请输入Token(meta_token)", "Please input user notes": "请输入备注", "Status": "状态", "Operation": "操作", "Confirm": "确定", "Cancel": "取消", "Confirm to remove user": "确定删除用户?", "Confirm to disable user": "确定禁用用户?", "Confirm to enable user": "确定启用用户?", "Operate success": "操作成功", "Operate failed": "操作失败", "Operate error": "操作异常", "Other error": "其他异常", "Param error": "参数异常", "User exist": "用户已经存在", "User not exist": "用户不存在", "User format error": "用户不能为空或包含空格。只允许英文数字、字母、下划线", "Token format error": "Token不能为空或包含空格。允许的特殊符号：_!@#$%^&*()", "Please check at least one user": "请选中需要操作的用户", "Operation confirm": "操作确认", "Empty data": "无数据", "Allowed ports": "允许端口", "Please input allowed ports": "请输入允许使用的端口,如:8081, 9000-9010", "Allowed domains": "允许域名", "Please input allowed domains": "请输入允许使用的域名,如:web01.domain.com,web02.domain.com", "Allowed subdomains": "允许子域名", "Please input allowed subdomains": "请输入允许使用的子域名,如:web01,web02", "Ports is invalid": "端口不正确", "Domains is invalid": "域名不正确", "Subdomains is invalid": "子域名不正确", "Comment is invalid": "备注不正确,不能包含换行", "Not limit": "无限制", "None": "无", "Server Info": "服务器信息", "Users": "用户列表", "Proxies": "代理列表", "Name": "名称", "Port": "端口", "Connections": "连接数", "Traffic In": "入站流量", "Traffic Out": "出站流量", "Client Version": "客户端版本", "Traffic Statistics": "流量统计", "Type": "连接类型", "Domains": "自定义域名", "SubDomain": "二级域名", "Locations": "路由", "HostRewrite": "自定义请求头", "Encryption": "数据加密", "Compression": "数据压缩", "Addr": "使用端口", "online": "在线", "offline": "离线", "true": "是", "false": "否", "Last Start": "上次连接时间", "Last Close": "上次断开时间", "Network Traffic": "网络流量", "today": "今日", "now": "当前", "Version": "版本号", "Bind Port": "TCP 端口", "KCP Bind Port": "KCP 端口", "QUIC Bind Port": "QUIC 端口", "HTTP Port": "HTTP 端口", "HTTPS Port": "HTTPS 端口", "TCPMUX Port": "TCPMUX 端口", "Subdomain Host": "二级域名后缀", "Max Pool Count": "每个代理最大连接池大小", "Max Ports Per Client": "单个客户端最大同时存在代理数", "Heart Beat Timeout": "心跳连接超时时间", "Allow Ports": "端口限制", "TLS Only": "仅接受启用 TLS 的客户端", "Current Connections": "当前连接数", "Client Counts": "客户端总数", "Proxy Counts": "代理总数", "Not Set": "未配置", "Proxy": "代理数量", "Username": "用户名", "Password": "密码", "Login": "登录", "Please input username": "请填写用户名", "Please input password": "请填写密码", "Login success": "登录成功", "Username or password incorrect": "用户名或密码错误", "Token invalid": "登录信息无效", "Total": "共", "Items": "条记录", "Go to": "到第", "Per Page": "条/页"}